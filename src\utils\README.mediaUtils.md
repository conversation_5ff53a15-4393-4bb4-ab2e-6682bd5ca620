# Media Utils Documentation

This module provides utilities for handling PDF and Audio content with AWS S3 base URLs.

## Overview

The media utilities automatically convert relative file paths to full AWS S3 URLs for consistent media access across the application.

### Base URLs

- **PDF Base URL**: `https://somayyaacademy.s3.ap-south-amazonaws.com/somayyaAcademyEdTech/Pdf/`
- **Audio Base URL**: `https://somayyaacademy.s3.ap-south-amazonaws.com/somayyaAcademyEdTech/Audio/`

## Core Functions

### `getPdfUrl(filename)`

Constructs full PDF URL from relative file path.

```javascript
import { getPdfUrl } from '../utils/mediaUtils';

// Basic usage
const pdfUrl = getPdfUrl('documents/filename.pdf');
// Returns: Full AWS S3 URL

// Handles leading slashes
const pdfUrl2 = getPdfUrl('/folder/filename.pdf');
// Returns: Full AWS S3 URL

// Absolute URLs are returned unchanged
const absoluteUrl = getPdfUrl('https://example.com/filename.pdf');
// Returns: https://example.com/filename.pdf
```

### `getAudioUrl(filename)`

Constructs full Audio URL from relative file path.

```javascript
import { getAudioUrl } from '../utils/mediaUtils';

const audioUrl = getAudioUrl('audio/filename.mp3');
// Returns: Full AWS S3 URL
```

### `validateMediaUrl(url)`

Validates if a media URL is accessible (async).

```javascript
import { validateMediaUrl } from '../utils/mediaUtils';

const isAccessible = await validateMediaUrl(mediaUrl);
// Returns: true if accessible, false otherwise
```

### `getMediaUrlWithFallback(filename, type, fallbackUrls)`

Gets media URL with fallback options (async).

```javascript
import { getMediaUrlWithFallback } from '../utils/mediaUtils';

const workingUrl = await getMediaUrlWithFallback(
  filename,
  'pdf',
  fallbackUrls
);
// Returns: First accessible URL or empty string
```

## Utility Functions

### File Validation

```javascript
import { hasValidMediaExtension, getMediaTypeFromFilename } from '../utils/mediaUtils';

// Check if file has valid extension
const isValidPdf = hasValidMediaExtension(filename, 'pdf');
const isValidAudio = hasValidMediaExtension(filename, 'audio');

// Detect media type from filename
const mediaType = getMediaTypeFromFilename(filename);
```

### URL Manipulation

```javascript
import { extractFilenameFromUrl } from '../utils/mediaUtils';

const filename = extractFilenameFromUrl(mediaUrl);
// Returns: extracted filename
```

### Performance Optimization

```javascript
import { preloadMediaUrl } from '../utils/mediaUtils';

// Preload media for better performance
const pdfPreloaded = await preloadMediaUrl(pdfUrl, 'pdf');
const audioPreloaded = await preloadMediaUrl(audioUrl, 'audio');
```

## React Component Usage

### Basic PDF Viewer

```jsx
import React, { useState, useEffect } from 'react';
import { getPdfUrl, validateMediaUrl } from '../utils/mediaUtils';

const PDFViewer = ({ pdfFile }) => {
  const [pdfUrl, setPdfUrl] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const loadPdf = async () => {
      if (!pdfFile) return;

      const fullUrl = getPdfUrl(pdfFile);
      const isAccessible = await validateMediaUrl(fullUrl);

      if (isAccessible) {
        setPdfUrl(fullUrl);
      } else {
        setError('PDF file not accessible');
      }

      setIsLoading(false);
    };

    loadPdf();
  }, [pdfFile]);

  if (isLoading) return <div>Loading PDF...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <iframe
      src={pdfUrl}
      width="100%"
      height="600px"
      title="PDF Viewer"
    />
  );
};
```

### Audio Player with Fallback

```jsx
import React, { useState, useEffect } from 'react';
import { getMediaUrlWithFallback } from '../utils/mediaUtils';

const AudioPlayer = ({ audioFile, fallbackUrls = [] }) => {
  const [audioUrl, setAudioUrl] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadAudio = async () => {
      const workingUrl = await getMediaUrlWithFallback(
        audioFile,
        'audio',
        fallbackUrls
      );

      setAudioUrl(workingUrl);
      setIsLoading(false);
    };

    if (audioFile) loadAudio();
  }, [audioFile, fallbackUrls]);

  if (isLoading) return <div>Loading audio...</div>;
  if (!audioUrl) return <div>Audio not available</div>;

  return (
    <audio controls>
      <source src={audioUrl} type="audio/mpeg" />
      Your browser does not support the audio element.
    </audio>
  );
};
```

## Error Handling

The utilities include comprehensive error handling:

- **Empty/null inputs**: Return empty strings or false
- **Invalid URLs**: Log warnings and return fallback values
- **Network errors**: Catch and handle gracefully
- **File validation**: Check extensions and formats

## Best Practices

1. **Always validate URLs** before using them in components
2. **Use fallback URLs** for critical media content
3. **Preload media** when possible for better performance
4. **Handle loading states** in your components
5. **Provide error messages** for failed media loads

## Supported File Types

### PDF Files
- `.pdf`

### Audio Files
- `.mp3`
- `.wav`
- `.ogg`
- `.m4a`
- `.aac`
- `.flac`

## Testing

The utilities include comprehensive tests covering:
- URL construction
- File validation
- Error handling
- Edge cases
- Async operations

Run tests with:
```bash
npm test src/utils/__tests__/mediaUtils.test.js
```
