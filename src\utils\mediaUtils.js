/**
 * Media URL Utilities
 * Provides functions to construct full URLs for PDF and Audio content from AWS S3
 */

import { PDF_BASE_URL, AUDIO_BASE_URL } from './constants.js';

/**
 * Check if we're in development mode
 * @returns {boolean} True if in development mode
 */
function isDevelopmentMode() {
  return import.meta.env.DEV || import.meta.env.MODE === 'development';
}

/**
 * Normalize a file path by removing leading slashes and ensuring proper format
 * @param {string} filePath - The file path to normalize
 * @returns {string} Normalized file path
 */
function normalizeFilePath(filePath) {
  if (!filePath || typeof filePath !== 'string') {
    return '';
  }

  // Remove leading slashes and whitespace
  return filePath.replace(/^\/+/, '').trim();
}

/**
 * Check if a URL is already absolute (has protocol)
 * @param {string} url - URL to check
 * @returns {boolean} True if URL is absolute
 */
function isAbsoluteUrl(url) {
  if (!url || typeof url !== 'string') {
    return false;
  }

  return /^https?:\/\//.test(url) || /^\/\//.test(url);
}

/**
 * Construct full PDF URL from relative file path
 * @param {string} filename - PDF filename or relative path
 * @returns {string} Full PDF URL
 */
export function getPdfUrl(filename) {
  if (!filename) {
    console.warn('getPdfUrl: No filename provided');
    return '';
  }

  // If already an absolute URL, return as-is
  if (isAbsoluteUrl(filename)) {
    console.log('getPdfUrl: Already absolute URL:', filename);
    return filename;
  }

  const normalizedPath = normalizeFilePath(filename);
  if (!normalizedPath) {
    console.warn('getPdfUrl: Invalid filename after normalization:', filename);
    return '';
  }

  // Use proxy URL in development to avoid CORS issues
  const fullUrl = isDevelopmentMode()
    ? `/api/pdf-proxy/${normalizedPath}`
    : `${PDF_BASE_URL}${normalizedPath}`;

  console.log('getPdfUrl: Constructed URL:', {
    filename,
    normalizedPath,
    fullUrl,
    isDev: isDevelopmentMode()
  });

  return fullUrl;
}

/**
 * Construct full Audio URL from relative file path
 * @param {string} filename - Audio filename or relative path
 * @returns {string} Full Audio URL
 */
export function getAudioUrl(filename) {
  if (!filename) {
    console.warn('getAudioUrl: No filename provided');
    return '';
  }

  // If already an absolute URL, return as-is
  if (isAbsoluteUrl(filename)) {
    console.log('getAudioUrl: Already absolute URL:', filename);
    return filename;
  }

  const normalizedPath = normalizeFilePath(filename);
  if (!normalizedPath) {
    console.warn('getAudioUrl: Invalid filename after normalization:', filename);
    return '';
  }

  const fullUrl = `${AUDIO_BASE_URL}${normalizedPath}`;
  console.log('getAudioUrl: Constructed URL:', { filename, normalizedPath, fullUrl });

  return fullUrl;
}

/**
 * Validate if a media URL is accessible
 * @param {string} url - URL to validate
 * @returns {Promise<boolean>} True if URL is accessible
 */
export async function validateMediaUrl(url) {
  if (!url) {
    return false;
  }

  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.warn('validateMediaUrl: URL not accessible:', { url, error: error.message });
    return false;
  }
}

/**
 * Get media URL with fallback options
 * @param {string} filename - Media filename
 * @param {string} type - Media type ('pdf' or 'audio')
 * @param {string[]} fallbackUrls - Array of fallback URLs to try
 * @returns {Promise<string>} First accessible URL or empty string
 */
export async function getMediaUrlWithFallback(filename, type, fallbackUrls = []) {
  const getUrlFunction = type === 'pdf' ? getPdfUrl : getAudioUrl;
  const primaryUrl = getUrlFunction(filename);

  // Try primary URL first
  if (await validateMediaUrl(primaryUrl)) {
    return primaryUrl;
  }

  console.warn(`Primary ${type} URL not accessible:`, primaryUrl);

  // Try fallback URLs
  for (const fallbackUrl of fallbackUrls) {
    if (await validateMediaUrl(fallbackUrl)) {
      console.log(`Using fallback ${type} URL:`, fallbackUrl);
      return fallbackUrl;
    }
  }

  console.error(`No accessible ${type} URL found for:`, filename);
  return '';
}

/**
 * Extract filename from a full URL
 * @param {string} url - Full URL
 * @returns {string} Extracted filename
 */
export function extractFilenameFromUrl(url) {
  if (!url || typeof url !== 'string') {
    return '';
  }

  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    return pathname.split('/').pop() || '';
  } catch (error) {
    // If URL parsing fails, try simple string manipulation
    return url.split('/').pop() || '';
  }
}

/**
 * Check if a filename has a valid media extension
 * @param {string} filename - Filename to check
 * @param {string} type - Media type ('pdf' or 'audio')
 * @returns {boolean} True if filename has valid extension
 */
export function hasValidMediaExtension(filename, type) {
  if (!filename || typeof filename !== 'string') {
    return false;
  }

  const extension = filename.toLowerCase().split('.').pop();

  if (type === 'pdf') {
    return extension === 'pdf';
  } else if (type === 'audio') {
    const validAudioExtensions = ['mp3', 'wav', 'ogg', 'm4a', 'aac', 'flac'];
    return validAudioExtensions.includes(extension);
  }

  return false;
}

/**
 * Get media type from filename extension
 * @param {string} filename - Filename to analyze
 * @returns {string|null} Media type ('pdf', 'audio') or null if unknown
 */
export function getMediaTypeFromFilename(filename) {
  if (hasValidMediaExtension(filename, 'pdf')) {
    return 'pdf';
  } else if (hasValidMediaExtension(filename, 'audio')) {
    return 'audio';
  }

  return null;
}

/**
 * Preload media URL to improve loading performance
 * @param {string} url - URL to preload
 * @param {string} type - Media type ('pdf' or 'audio')
 * @returns {Promise<boolean>} True if preload successful
 */
export async function preloadMediaUrl(url, type) {
  if (!url) {
    return false;
  }

  try {
    if (type === 'pdf') {
      // For PDFs, we can use a simple fetch to preload
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } else if (type === 'audio') {
      // For audio, we can create an Audio object to preload
      return new Promise((resolve) => {
        const audio = new Audio();
        audio.addEventListener('canplaythrough', () => resolve(true), { once: true });
        audio.addEventListener('error', () => resolve(false), { once: true });
        audio.src = url;
        audio.load();
      });
    }

    return false;
  } catch (error) {
    console.warn('preloadMediaUrl: Preload failed:', { url, type, error: error.message });
    return false;
  }
}
