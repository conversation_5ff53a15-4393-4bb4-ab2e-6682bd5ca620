import React, { useState, useEffect } from 'react';
import { LoadingSpinner } from '../ui';
import '../../styles/SimplePDFViewer.css';

/**
 * Simple PDF Viewer using iframe to avoid CORS issues
 * This replaces the react-pdf-highlighter PdfLoader component
 */
const SimplePDFViewer = ({ pdfUrl, textSize, className = '' }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // Reset states when PDF URL changes
  useEffect(() => {
    if (pdfUrl) {
      setIsLoading(true);
      setHasError(false);
      setErrorMessage('');
    }
  }, [pdfUrl]);

  // Handle iframe load events
  const handleIframeLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleIframeError = () => {
    setIsLoading(false);
    setHasError(true);
    setErrorMessage('Failed to load PDF. The file may not be accessible or may not exist.');
  };

  // Handle cases where PDF URL is not provided
  if (!pdfUrl) {
    return (
      <div className={`simple-pdf-viewer ${className}`}>
        <div className="pdf-error">
          <p>No PDF URL provided</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`simple-pdf-viewer ${textSize} ${className}`}>
      {/* Loading indicator */}
      {isLoading && (
        <div className="pdf-loading-overlay">
          <LoadingSpinner size="medium" />
          <p>Loading PDF...</p>
        </div>
      )}

      {/* Error state */}
      {hasError && (
        <div className="pdf-error">
          <h3>PDF Loading Error</h3>
          <p>{errorMessage}</p>
          <p>
            <strong>Note:</strong> You can try opening the PDF directly in a new tab:
          </p>
          <a 
            href={pdfUrl} 
            target="_blank" 
            rel="noopener noreferrer"
            className="pdf-direct-link"
          >
            Open PDF in New Tab
          </a>
        </div>
      )}

      {/* PDF iframe */}
      <iframe
        src={pdfUrl}
        title="PDF Viewer"
        className="pdf-iframe"
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        style={{ display: hasError ? 'none' : 'block' }}
      />

      {/* Information about highlighting limitations */}
      {!isLoading && !hasError && (
        <div className="pdf-info-banner">
          <p>
            📝 <strong>Note:</strong> Advanced highlighting features are temporarily disabled to resolve loading issues. 
            You can still view and read the PDF content.
          </p>
        </div>
      )}
    </div>
  );
};

export default SimplePDFViewer;
